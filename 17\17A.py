import matplotlib.pyplot as plt
import numpy as np
import time
import random

# 测试数据结构性能
class BST:
    def __init__(self, val):
        self.val = val
        self.left = None
        self.right = None
    
    def insert(self, val):
        if val < self.val:
            if self.left is None:
                self.left = BST(val)
            else:
                self.left.insert(val)
        else:
            if self.right is None:
                self.right = BST(val)
            else:
                self.right.insert(val)

class HashTable:
    def __init__(self, size=1000):
        self.size = size
        self.table = [[] for _ in range(size)]
    
    def insert(self, key, val):
        hash_key = hash(key) % self.size
        bucket = self.table[hash_key]
        for i, (k, v) in enumerate(bucket):
            if k == key:
                bucket[i] = (key, val)
                return
        bucket.append((key, val))

class DynamicArray:
    def __init__(self):
        self.capacity = 1
        self.size = 0
        self.data = [None] * self.capacity
    
    def insert(self, val):
        if self.size == self.capacity:
            self.capacity *= 2
            new_data = [None] * self.capacity
            for i in range(self.size):
                new_data[i] = self.data[i]
            self.data = new_data
        self.data[self.size] = val
        self.size += 1

class ListNode:
    def __init__(self, val):
        self.val = val
        self.next = None

class LinkedList:
    def __init__(self):
        self.head = None
        self.size = 0
    
    def insert(self, val):
        new_node = ListNode(val)
        current = self.head
        for i in range(self.size):
            if current.next is None:
                break
            current = current.next
        if current is None:
            self.head = new_node
        else:
            current.next = new_node
        self.size += 1

# 性能测试
sizes = [100, 200, 500, 1000, 2000, 5000]
bst_times = []
hash_times = []
array_times = []
list_times = []

for size in sizes:
    # BST测试
    bst = BST(random.randint(1, 1000))
    start = time.time()
    for i in range(size):
        bst.insert(random.randint(1, 1000))
    bst_times.append(time.time() - start)
    
    # 哈希表测试
    ht = HashTable()
    start = time.time()
    for i in range(size):
        ht.insert(i, random.randint(1, 1000))
    hash_times.append(time.time() - start)
    
    # 动态数组测试
    arr = DynamicArray()
    start = time.time()
    for i in range(size):
        arr.insert(random.randint(1, 1000))
    array_times.append(time.time() - start)
    
    # 链表测试
    ll = LinkedList()
    start = time.time()
    for i in range(size):
        ll.insert(random.randint(1, 1000))
    list_times.append(time.time() - start)

# 绘制性能对比图
plt.figure(figsize=(12, 8))
plt.subplot(2, 2, 1)
plt.plot(sizes, bst_times, 'ro-', label='BST Insert')
plt.xlabel('Data Size')
plt.ylabel('Time (seconds)')
plt.title('Binary Search Tree')
plt.legend()

plt.subplot(2, 2, 2)
plt.plot(sizes, hash_times, 'go-', label='Hash Table Insert')
plt.xlabel('Data Size')
plt.ylabel('Time (seconds)')
plt.title('Hash Table')
plt.legend()

plt.subplot(2, 2, 3)
plt.plot(sizes, array_times, 'bo-', label='Dynamic Array Insert')
plt.xlabel('Data Size')
plt.ylabel('Time (seconds)')
plt.title('Dynamic Array')
plt.legend()

plt.subplot(2, 2, 4)
plt.plot(sizes, list_times, 'mo-', label='Linked List Insert')
plt.xlabel('Data Size')
plt.ylabel('Time (seconds)')
plt.title('Linked List (with bug)')
plt.legend()

plt.tight_layout()
plt.show()
