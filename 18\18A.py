import cv2
import numpy as np
import matplotlib.pyplot as plt

# 创建测试图像
def create_test_image():
    image = np.zeros((200, 200), dtype=np.uint8)
    # 添加一些几何形状
    cv2.rectangle(image, (50, 50), (100, 100), 255, -1)
    cv2.circle(image, (150, 150), 30, 128, -1)
    cv2.line(image, (20, 180), (180, 20), 200, 3)
    return image

# 定义滤波器核
sobel_x = np.array([[-1, 0, 1],
                    [-2, 0, 2],
                    [-1, 0, 1]])

sharpen = np.array([[ 0, -1,  0],
                    [-1,  5, -1],
                    [ 0, -1,  0]])

gaussian = np.array([[1, 2, 1],
                     [2, 4, 2],
                     [1, 2, 1]]) / 16

# 错误的拉普拉斯滤波器（符号错误）
laplacian = np.array([[ 0,  1,  0],
                      [ 1, -4,  1],
                      [ 0,  1,  0]])

def apply_filter(image, kernel):
    return cv2.filter2D(image, -1, kernel)

# 创建测试图像
image = create_test_image()

# 应用滤波器
filtered_sobel = apply_filter(image, sobel_x)
filtered_sharpen = apply_filter(image, sharpen)
filtered_gaussian = apply_filter(image, gaussian)
filtered_laplacian = apply_filter(image, laplacian)

# 显示结果
plt.figure(figsize=(15, 12))

plt.subplot(3, 2, 1)
plt.imshow(image, cmap='gray')
plt.title('Original Image')
plt.axis('off')

plt.subplot(3, 2, 2)
plt.imshow(filtered_sobel, cmap='gray')
plt.title('Sobel X Filter')
plt.axis('off')

plt.subplot(3, 2, 3)
plt.imshow(filtered_sharpen, cmap='gray')
plt.title('Sharpen Filter')
plt.axis('off')

plt.subplot(3, 2, 4)
plt.imshow(filtered_gaussian, cmap='gray')
plt.title('Gaussian Blur Filter')
plt.axis('off')

plt.subplot(3, 2, 5)
plt.imshow(filtered_laplacian, cmap='gray')
plt.title('Laplacian Filter (with sign error)')
plt.axis('off')

plt.subplot(3, 2, 6)
# 显示正确的拉普拉斯滤波器效果作为对比
correct_laplacian = np.array([[ 0, -1,  0],
                              [-1,  4, -1],
                              [ 0, -1,  0]])
filtered_correct = apply_filter(image, correct_laplacian)
plt.imshow(filtered_correct, cmap='gray')
plt.title('Correct Laplacian Filter')
plt.axis('off')

plt.tight_layout()
plt.show()
