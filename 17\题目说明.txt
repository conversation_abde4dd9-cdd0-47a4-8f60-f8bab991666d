题目17：数据结构可视化与时间复杂度分析

prompt（问题）：
观察图片中显示的四种数据结构操作的性能对比图，根据图中的时间复杂度曲线特征，判断以下哪个代码片段中存在一个微妙的错误，导致其时间复杂度与理论预期不符？

选项A：
# 二分搜索树插入操作
class BST:
    def __init__(self, val):
        self.val = val
        self.left = None
        self.right = None
    
    def insert(self, val):
        if val < self.val:
            if self.left is None:
                self.left = BST(val)
            else:
                self.left.insert(val)
        else:
            if self.right is None:
                self.right = BST(val)
            else:
                self.right.insert(val)

选项B：
# 哈希表插入操作
class HashTable:
    def __init__(self, size=1000):
        self.size = size
        self.table = [[] for _ in range(size)]
    
    def insert(self, key, val):
        hash_key = hash(key) % self.size
        bucket = self.table[hash_key]
        for i, (k, v) in enumerate(bucket):
            if k == key:
                bucket[i] = (key, val)
                return
        bucket.append((key, val))

选项C：
# 动态数组插入操作
class DynamicArray:
    def __init__(self):
        self.capacity = 1
        self.size = 0
        self.data = [None] * self.capacity
    
    def insert(self, val):
        if self.size == self.capacity:
            self.capacity *= 2
            new_data = [None] * self.capacity
            for i in range(self.size):
                new_data[i] = self.data[i]
            self.data = new_data
        self.data[self.size] = val
        self.size += 1

选项D：
# 链表插入操作
class LinkedList:
    def __init__(self):
        self.head = None
        self.size = 0
    
    def insert(self, val):
        new_node = ListNode(val)
        current = self.head
        for i in range(self.size):
            if current.next is None:
                break
            current = current.next
        if current is None:
            self.head = new_node
        else:
            current.next = new_node
        self.size += 1

answer（答案）：D

推理过程：
1）观察图片中的性能曲线，链表的插入操作显示为O(n)时间复杂度而非预期的O(1)；
2）分析选项D的代码，发现insert方法中使用了for循环遍历到链表末尾才插入新节点；
3）正确的链表头部插入应该是O(1)操作，但这里实现的是尾部插入，需要遍历整个链表，导致O(n)复杂度；
4）其他选项的实现都符合理论预期：BST平均O(log n)，哈希表平均O(1)，动态数组摊销O(1)。
