import matplotlib.pyplot as plt
import numpy as np

def draw_tree(x, y, angle, length, depth):
    if depth == 0:
        return
    
    x2 = x + length * np.cos(angle)
    y2 = y + length * np.sin(angle)
    plt.plot([x, x2], [y, y2], 'brown', linewidth=depth)
    
    draw_tree(x2, y2, angle - np.pi/6, length*0.7, depth-1)
    draw_tree(x2, y2, angle + np.pi/6, length*0.7, depth-1)

plt.figure(figsize=(10, 8))
draw_tree(0, 0, np.pi/2, 100, 8)
plt.axis('equal')
plt.title('Fractal Tree with 30° Branch Angles')
plt.show()
