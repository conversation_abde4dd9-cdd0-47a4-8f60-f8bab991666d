import threading
import time
import matplotlib.pyplot as plt
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import random

# 有问题的Counter实现（选项C）
class ProblematicCounter:
    def __init__(self):
        self.value = 0
        self.lock = threading.Lock()
        self.access_log = []
        self.log_lock = threading.Lock()
    
    def increment(self, thread_id):
        try:
            self.lock.acquire()
            # 记录访问开始
            with self.log_lock:
                self.access_log.append((time.time(), thread_id, 'start', self.value))
            
            temp = self.value
            time.sleep(0.001 + random.uniform(0, 0.002))  # 模拟处理时间
            
            # 模拟偶发异常
            if random.random() < 0.1:  # 10%概率发生异常
                raise Exception(f"Simulated exception in thread {thread_id}")
            
            self.value = temp + 1
            
            # 记录访问结束
            with self.log_lock:
                self.access_log.append((time.time(), thread_id, 'end', self.value))
            
            self.lock.release()
        except Exception as e:
            # 注意：这里没有释放锁！这是问题所在
            with self.log_lock:
                self.access_log.append((time.time(), thread_id, 'error', self.value))
            print(f"Exception in thread {thread_id}: {e}")

# 正确的Counter实现（对比用）
class CorrectCounter:
    def __init__(self):
        self.value = 0
        self.lock = threading.Lock()
        self.access_log = []
        self.log_lock = threading.Lock()
    
    def increment(self, thread_id):
        self.lock.acquire()
        try:
            # 记录访问开始
            with self.log_lock:
                self.access_log.append((time.time(), thread_id, 'start', self.value))
            
            temp = self.value
            time.sleep(0.001 + random.uniform(0, 0.002))  # 模拟处理时间
            
            # 模拟偶发异常
            if random.random() < 0.1:  # 10%概率发生异常
                raise Exception(f"Simulated exception in thread {thread_id}")
            
            self.value = temp + 1
            
            # 记录访问结束
            with self.log_lock:
                self.access_log.append((time.time(), thread_id, 'end', self.value))
                
        except Exception as e:
            with self.log_lock:
                self.access_log.append((time.time(), thread_id, 'error', self.value))
            print(f"Exception in thread {thread_id}: {e}")
        finally:
            self.lock.release()  # 确保锁被释放

def run_test(counter_class, num_threads=4, num_operations=20):
    counter = counter_class()
    
    def worker(thread_id):
        for i in range(num_operations // num_threads):
            try:
                counter.increment(thread_id)
                time.sleep(0.001)
            except:
                break  # 如果发生死锁，线程会被阻塞
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(worker, i) for i in range(num_threads)]
        
        # 等待所有任务完成，但设置超时
        for future in futures:
            try:
                future.result(timeout=5)  # 5秒超时
            except:
                print("Thread timed out - possible deadlock!")
    
    return counter

# 运行测试
print("Testing Problematic Counter (Option C)...")
problematic_counter = run_test(ProblematicCounter)

print("\nTesting Correct Counter (with proper exception handling)...")
correct_counter = run_test(CorrectCounter)

# 可视化结果
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# 绘制有问题的计数器的时序图
if problematic_counter.access_log:
    times = [log[0] - problematic_counter.access_log[0][0] for log in problematic_counter.access_log]
    thread_ids = [log[1] for log in problematic_counter.access_log]
    actions = [log[2] for log in problematic_counter.access_log]
    values = [log[3] for log in problematic_counter.access_log]
    
    colors = {'start': 'green', 'end': 'blue', 'error': 'red'}
    for i, (time_val, thread_id, action, value) in enumerate(zip(times, thread_ids, actions, values)):
        ax1.scatter(time_val, thread_id, c=colors[action], s=50, alpha=0.7)
        if action == 'error':
            ax1.annotate('EXCEPTION!', (time_val, thread_id), 
                        xytext=(5, 5), textcoords='offset points', 
                        fontsize=8, color='red', weight='bold')

ax1.set_title('Problematic Counter - Thread Access Timeline (Option C)')
ax1.set_xlabel('Time (seconds)')
ax1.set_ylabel('Thread ID')
ax1.legend(['Start', 'End', 'Error'])
ax1.grid(True, alpha=0.3)

# 绘制正确计数器的时序图
if correct_counter.access_log:
    times = [log[0] - correct_counter.access_log[0][0] for log in correct_counter.access_log]
    thread_ids = [log[1] for log in correct_counter.access_log]
    actions = [log[2] for log in correct_counter.access_log]
    
    for i, (time_val, thread_id, action, value) in enumerate(zip(times, thread_ids, actions, values)):
        ax2.scatter(time_val, thread_id, c=colors[action], s=50, alpha=0.7)

ax2.set_title('Correct Counter - Thread Access Timeline (with proper exception handling)')
ax2.set_xlabel('Time (seconds)')
ax2.set_ylabel('Thread ID')
ax2.legend(['Start', 'End', 'Error'])
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 打印结果统计
print(f"\nResults:")
print(f"Problematic Counter final value: {problematic_counter.value}")
print(f"Correct Counter final value: {correct_counter.value}")
print(f"Expected value: 20")

print(f"\nProblematic Counter access log entries: {len(problematic_counter.access_log)}")
print(f"Correct Counter access log entries: {len(correct_counter.access_log)}")

# 分析错误类型
prob_errors = sum(1 for log in problematic_counter.access_log if log[2] == 'error')
correct_errors = sum(1 for log in correct_counter.access_log if log[2] == 'error')
print(f"\nExceptions occurred:")
print(f"Problematic Counter: {prob_errors}")
print(f"Correct Counter: {correct_errors}")
print(f"\n⚠️  The problematic counter may cause deadlocks when exceptions occur!")
