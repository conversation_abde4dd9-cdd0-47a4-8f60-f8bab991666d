题目18：图像滤波器卷积核效果对比分析

prompt（问题）：
观察图片中显示的四种不同卷积核对同一张图像的滤波效果，根据滤波后图像的特征，判断以下哪个卷积核定义中存在一个细微的符号错误？

选项A：
# 边缘检测滤波器（Sobel X方向）
sobel_x = np.array([[-1, 0, 1],
                    [-2, 0, 2],
                    [-1, 0, 1]])

选项B：
# 锐化滤波器
sharpen = np.array([[ 0, -1,  0],
                    [-1,  5, -1],
                    [ 0, -1,  0]])

选项C：
# 高斯模糊滤波器
gaussian = np.array([[1, 2, 1],
                     [2, 4, 2],
                     [1, 2, 1]]) / 16

选项D：
# 拉普拉斯边缘检测滤波器
laplacian = np.array([[ 0,  1,  0],
                      [ 1, -4,  1],
                      [ 0,  1,  0]])

图像处理代码：
import cv2
import numpy as np
import matplotlib.pyplot as plt

def apply_filter(image, kernel):
    return cv2.filter2D(image, -1, kernel)

# 加载图像并应用滤波器
image = cv2.imread('test_image.jpg', cv2.IMREAD_GRAYSCALE)
filtered_images = []
for kernel in [sobel_x, sharpen, gaussian, laplacian]:
    filtered = apply_filter(image, kernel)
    filtered_images.append(filtered)

answer（答案）：D

推理过程：
1）观察图片中拉普拉斯滤波器的效果，发现边缘检测结果出现了反色现象（边缘为黑色而非白色）；
2）标准的拉普拉斯算子中心值应该为正数，周围值为负数，或者相反，但符号必须保持一致的对比关系；
3）选项D中的拉普拉斯核中心为-4，周围为+1，这会导致边缘检测结果反转，与图片中观察到的效果一致；
4）正确的拉普拉斯核应该是中心为+4，周围为-1，或者整体乘以-1，但选项D的符号配置导致了错误的滤波效果。
