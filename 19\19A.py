import re
import matplotlib.pyplot as plt
import numpy as np

# 定义正则表达式模式
email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
phone_pattern = r'^1[3-9]\d{9}$'
id_pattern = r'^\d{17}[\dXx]$'
ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'  # 存在边界条件错误

def test_patterns():
    # 测试用例
    test_cases = [
        # 电子邮件测试用例
        ['<EMAIL>', '<EMAIL>', 'invalid.email', 'user@.com', '<EMAIL>'],
        # 手机号码测试用例  
        ['13812345678', '15987654321', '12345678901', '1381234567', '19876543210'],
        # 身份证号码测试用例
        ['12345678901234567X', '123456789012345678', '1234567890123456789', '12345678901234567x', '123456789012345670'],
        # IP地址测试用例
        ['***********', '***************', '999.999.999.999', '192.168.1', '0.0.0.0']
    ]
    
    patterns = [email_pattern, phone_pattern, id_pattern, ip_pattern]
    pattern_names = ['Email', 'Phone', 'ID Card', 'IP Address']
    results = []
    
    for i, pattern in enumerate(patterns):
        matches = []
        for test_string in test_cases[i]:
            match = bool(re.match(pattern, test_string))
            matches.append(match)
        results.append(matches)
    
    return results, test_cases, pattern_names

# 运行测试
results, test_cases, pattern_names = test_patterns()

# 可视化结果
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
axes = axes.flatten()

colors = ['green', 'red']
labels = ['Match', 'No Match']

for i, (result, test_case, name) in enumerate(zip(results, test_cases, pattern_names)):
    ax = axes[i]
    
    # 创建条形图显示匹配结果
    x_pos = np.arange(len(test_case))
    colors_list = ['green' if match else 'red' for match in result]
    
    bars = ax.bar(x_pos, [1] * len(test_case), color=colors_list, alpha=0.7)
    
    # 设置标签
    ax.set_title(f'{name} Pattern Matching Results')
    ax.set_xlabel('Test Cases')
    ax.set_ylabel('Match Result')
    ax.set_xticks(x_pos)
    ax.set_xticklabels([f'Test{j+1}' for j in range(len(test_case))], rotation=45)
    
    # 添加测试字符串作为注释
    for j, (bar, test_str, match) in enumerate(zip(bars, test_case, result)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{test_str}\n{"✓" if match else "✗"}',
                ha='center', va='bottom', fontsize=8, rotation=0)

# 添加图例
handles = [plt.Rectangle((0,0),1,1, color=c, alpha=0.7) for c in colors]
fig.legend(handles, labels, loc='upper right')

plt.tight_layout()
plt.show()

# 打印详细结果
print("Detailed Test Results:")
print("=" * 50)
for i, (result, test_case, name) in enumerate(zip(results, test_cases, pattern_names)):
    print(f"\n{name} Pattern: {[email_pattern, phone_pattern, id_pattern, ip_pattern][i]}")
    for j, (test_str, match) in enumerate(zip(test_case, result)):
        status = "MATCH" if match else "NO MATCH"
        print(f"  Test {j+1}: '{test_str}' -> {status}")
    
    # 特别标注IP地址的问题
    if name == "IP Address":
        print("  ⚠️  Note: This pattern incorrectly accepts '999.999.999.999' (out of valid IP range)")
